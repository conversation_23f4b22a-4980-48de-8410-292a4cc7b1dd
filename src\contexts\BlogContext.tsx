"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { getBlogs, getSingleBlog } from "@/lib/api/blogs";
import { type Blog, type BlogsQueryParams } from "@/types/blog";

interface BlogContextType {
  // Latest blogs state
  latestBlogs: Blog[];
  latestBlogsLoading: boolean;
  latestBlogsError: string | null;
  
  // Recent blogs state
  recentBlogs: Blog[];
  recentBlogsLoading: boolean;
  recentBlogsError: string | null;
  
  // Single blog state
  currentBlog: Blog | null;
  currentBlogLoading: boolean;
  currentBlogError: string | null;
  
  // Actions
  fetchLatestBlogs: (params?: BlogsQueryParams) => Promise<void>;
  fetchRecentBlogs: (params?: BlogsQueryParams) => Promise<void>;
  fetchBlogBySlug: (slug: string) => Promise<void>;
  clearCurrentBlog: () => void;
}

const BlogContext = createContext<BlogContextType | undefined>(undefined);

interface BlogProviderProps {
  children: ReactNode;
}

export const BlogProvider: React.FC<BlogProviderProps> = ({ children }) => {
  // Latest blogs state
  const [latestBlogs, setLatestBlogs] = useState<Blog[]>([]);
  const [latestBlogsLoading, setLatestBlogsLoading] = useState(false);
  const [latestBlogsError, setLatestBlogsError] = useState<string | null>(null);
  
  // Recent blogs state
  const [recentBlogs, setRecentBlogs] = useState<Blog[]>([]);
  const [recentBlogsLoading, setRecentBlogsLoading] = useState(false);
  const [recentBlogsError, setRecentBlogsError] = useState<string | null>(null);
  
  // Single blog state
  const [currentBlog, setCurrentBlog] = useState<Blog | null>(null);
  const [currentBlogLoading, setCurrentBlogLoading] = useState(false);
  const [currentBlogError, setCurrentBlogError] = useState<string | null>(null);

  const fetchLatestBlogs = async (params: BlogsQueryParams = {}) => {
    setLatestBlogsLoading(true);
    setLatestBlogsError(null);
    
    try {
      const defaultParams = {
        limit: 2,
        sortBy: "publishedAt" as const,
        sortOrder: "desc" as const,
        status: "published" as const,
        ...params,
      };
      
      const response = await getBlogs(defaultParams);
      
      if (response.success && response.data) {
        setLatestBlogs(response.data);
      } else {
        setLatestBlogsError(response.message || "Failed to fetch latest blogs");
      }
    } catch (error) {
      setLatestBlogsError("An unexpected error occurred");
      console.error("Error fetching latest blogs:", error);
    } finally {
      setLatestBlogsLoading(false);
    }
  };

  const fetchRecentBlogs = async (params: BlogsQueryParams = {}) => {
    setRecentBlogsLoading(true);
    setRecentBlogsError(null);
    
    try {
      const defaultParams = {
        limit: 6,
        sortBy: "publishedAt" as const,
        sortOrder: "desc" as const,
        status: "published" as const,
        ...params,
      };
      
      const response = await getBlogs(defaultParams);
      
      if (response.success && response.data) {
        console.log("recentBlogs", response.data);
        setRecentBlogs(response.data);
      } else {
        setRecentBlogsError(response.message || "Failed to fetch recent blogs");
      }
    } catch (error) {
      setRecentBlogsError("An unexpected error occurred");
      console.error("Error fetching recent blogs:", error);
    } finally {
      setRecentBlogsLoading(false);
    }
  };

  const fetchBlogBySlug = async (slug: string) => {
    setCurrentBlogLoading(true);
    setCurrentBlogError(null);
    
    try {
      // Note: If your API doesn't support slug-based lookup, you might need to:
      // 1. Modify your backend to accept slugs
      // 2. Or fetch all blogs and find by slug
      // For now, assuming the API accepts slug as ID
      const response = await getSingleBlog(slug);
      
      if (response.success && response.data) {
        setCurrentBlog(response.data);
      } else {
        setCurrentBlogError(response.message || "Blog not found");
      }
    } catch (error) {
      setCurrentBlogError("An unexpected error occurred");
      console.error("Error fetching blog:", error);
    } finally {
      setCurrentBlogLoading(false);
    }
  };

  const clearCurrentBlog = () => {
    setCurrentBlog(null);
    setCurrentBlogError(null);
  };

  const value: BlogContextType = {
    latestBlogs,
    latestBlogsLoading,
    latestBlogsError,
    recentBlogs,
    recentBlogsLoading,
    recentBlogsError,
    currentBlog,
    currentBlogLoading,
    currentBlogError,
    fetchLatestBlogs,
    fetchRecentBlogs,
    fetchBlogBySlug,
    clearCurrentBlog,
  };

  return (
    <BlogContext.Provider value={value}>
      {children}
    </BlogContext.Provider>
  );
};

export const useBlog = (): BlogContextType => {
  const context = useContext(BlogContext);
  if (context === undefined) {
    throw new Error("useBlog must be used within a BlogProvider");
  }
  return context;
};
