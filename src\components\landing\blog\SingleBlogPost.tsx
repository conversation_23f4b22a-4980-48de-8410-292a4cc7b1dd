"use client";

import React, { useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { FileIcon } from "@radix-ui/react-icons";
import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useBlog } from "@/contexts/BlogContext";
import LoadingSpinner from "@/components/common/LoadingSpinner";

type SingleBlogPostProps = {
  slug: string;
};

const SingleBlogPost = ({ slug }: SingleBlogPostProps) => {
  const {
    currentBlog,
    currentBlogLoading,
    currentBlogError,
    fetchBlogBySlug,
    clearCurrentBlog
  } = useBlog();

  useEffect(() => {
    if (slug) {
      fetchBlogBySlug(slug);
    }

    return () => {
      clearCurrentBlog();
    };
  }, [slug]);

  // Use only API data
  if (!currentBlog && !currentBlogLoading) {
    return (
      <div className="min-h-screen w-full flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-500 text-xl mb-4">No Data</p>
          <Link
            href="/blog"
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Back to Blogs
          </Link>
        </div>
      </div>
    );
  }

  // Normalize data structure for API data
  const post = currentBlog ? {
    id: currentBlog._id,
    title: currentBlog.title,
    excerpt: currentBlog.content.substring(0, 200) + "...",
    content: currentBlog.content,
    featuredImage: currentBlog.imageUrl || "/assets/images/business.png",
    category: currentBlog.categories?.[0] || "General",
    tags: currentBlog.tags || [],
    date: new Date(currentBlog.publishedAt).toLocaleDateString(),
    readTime: currentBlog.readTime ? `${currentBlog.readTime} min read` : "5 min read",
    author: {
      name: currentBlog.author,
      avatar: "/assets/images/testimonial1.jpg",
      bio: "Content Author"
    },
    slug: currentBlog.slug || currentBlog._id
  } : null;

  if (currentBlogLoading) {
    return (
      <div className="min-h-screen w-full flex items-center justify-center">
        <LoadingSpinner width={60} height={60} />
      </div>
    );
  }

  if (currentBlogError && !currentBlog) {
    return (
      <div className="min-h-screen w-full flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-500 mb-4">{currentBlogError}</p>
          <button
            onClick={() => fetchBlogBySlug(slug)}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 mr-4"
          >
            Try Again
          </button>
          <Link
            href="/blog"
            className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            Back to Blogs
          </Link>
        </div>
      </div>
    );
  }

  if (!post) {
    return (
      <div className="min-h-screen w-full flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-500 text-xl mb-4">No Data</p>
          <Link
            href="/blog"
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Back to Blogs
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen w-full">
      {/* Navigation Breadcrumb */}
      <div className="mx-auto px-[5%] pb-6">
        <nav className="mx-auto mb-16 flex max-w-[1408px] items-center space-x-2 text-lg font-semibold text-gray-400">
          <Link href="/blog" className="transition-colors hover:text-accent">
            Blogs
          </Link>
          <span>›</span>
          <span className="text-accent">{post.title}</span>
        </nav>

        {/* Header Section */}
        <div className="mx-auto mb-10 flex max-w-[1408px] flex-col lg:flex-row lg:gap-12">
          {/* Left Content */}
          <div className="lg:w-1/2">
            <h1 className="mb-4 text-4xl font-bold leading-tight text-accent">
              {post.title}
            </h1>

            <p className="mb-5 leading-relaxed text-black">{post.excerpt}</p>

            <div className="mb-6 flex items-center gap-6 text-sm font-semibold text-[#B4B4B4]">
              <span>{post.date}</span>
              <div className="flex items-center gap-2">
                <FileIcon />
                <span>{post.readTime}</span>
              </div>
            </div>

            {/* Author Info */}
            <div className="flex items-center">
              <div className="mr-3 h-10 w-10 overflow-hidden rounded-full">
                <Image
                  src={post.author.avatar}
                  alt={post.author.name}
                  width={40}
                  height={40}
                  className="object-cover"
                />
              </div>
              <span className="font-medium text-black">{post.author.name}</span>
            </div>
          </div>

          {/* Right Image */}
          <div className="mt-8 lg:mt-0 lg:w-1/2">
            <div className="relative h-64 overflow-hidden rounded-lg lg:h-80">
              <Image
                src={post.featuredImage}
                alt="Blog post featured image"
                fill
                className="object-cover"
              />
            </div>
          </div>
        </div>

        {/* Article Content */}
        <div className="mx-auto max-w-7xl">
          <div
            className="prose prose-lg prose-headings:text-gray-900 prose-p:text-gray-700 prose-p:leading-relaxed max-w-none"
            dangerouslySetInnerHTML={{ __html: post.content }}
          />
        </div>

        {/* Comments Section */}
        <div className="mx-auto mt-16 max-w-7xl">
          {/* Divider */}
          <div className="mb-10 h-[3px] w-full bg-gray-200" />

          {/* Comments Header */}
          <h2 className="mb-8 text-2xl font-bold text-accent">Comments</h2>

          {/* Comments List */}
          <div className="mb-12 space-y-6">
            {/* Comment 1 */}
            <div className="flex space-x-4">
              <div className="h-12 w-12 flex-shrink-0 overflow-hidden rounded-full">
                <Image
                  src="/assets/images/testimonial1.jpg"
                  alt="David Jason"
                  width={48}
                  height={48}
                  className="object-cover"
                />
              </div>
              <div className="flex-1">
                <div className="mb-1">
                  <h4 className="font-semibold text-black">David Jason</h4>
                  <p className="text-xs text-[#898989]">June 20, 2002</p>
                </div>
                <p className="text-sm leading-relaxed text-[#636363]">
                  Lorem ipsum Lorem ipsum Lorem ipsum Lorem ipsum Lorem ipsum
                  Lorem ipsum Lorem ipsum
                </p>
              </div>
            </div>

            {/* Comment 2 */}
            <div className="flex space-x-4">
              <div className="h-12 w-12 flex-shrink-0 overflow-hidden rounded-full">
                <Image
                  src="/assets/images/testimonial2.jpg"
                  alt="David Jason"
                  width={48}
                  height={48}
                  className="object-cover"
                />
              </div>
              <div className="flex-1">
                <div className="mb-1">
                  <h4 className="font-semibold text-black">David Jason</h4>
                  <p className="text-xs text-[#898989]">June 20, 2002</p>
                </div>
                <p className="text-sm leading-relaxed text-[#636363]">
                  Lorem ipsum Lorem ipsum Lorem ipsum Lorem ipsum Lorem ipsum
                  Lorem ipsum Lorem ipsum
                </p>
              </div>
            </div>
          </div>

          {/* Leave a Reply Section */}
          <div>
            <h3 className="mb-6 text-xl font-bold text-accent">
              Leave a reply
            </h3>
            <form className="space-y-4">
              {/* Message Textarea */}
              <div>
                <textarea
                  placeholder="Type a message"
                  rows={6}
                  className="w-full rounded-lg border border-gray-300 px-4 py-3 text-gray-700 placeholder-gray-500 focus:border-accent focus:outline-none focus:ring-1 focus:ring-accent"
                />
              </div>

              {/* Name Input */}
              <div>
                <input
                  type="text"
                  placeholder="Name"
                  className="w-full rounded-lg border border-gray-300 px-4 py-3 text-gray-700 placeholder-gray-500 focus:border-accent focus:outline-none focus:ring-1 focus:ring-accent"
                />
              </div>

              {/* Email Input */}
              <div>
                <input
                  type="email"
                  placeholder="Email"
                  className="w-full rounded-lg border border-gray-300 px-4 py-3 text-gray-700 placeholder-gray-500 focus:border-accent focus:outline-none focus:ring-1 focus:ring-accent"
                />
              </div>

              {/* Submit Button */}
              <div>
                <button
                  type="submit"
                  className={cn(
                    buttonVariants({ variant: "default", size: "lg" }),
                    "w-full rounded-lg px-6 py-3 font-medium",
                  )}
                >
                  Submit
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SingleBlogPost;
