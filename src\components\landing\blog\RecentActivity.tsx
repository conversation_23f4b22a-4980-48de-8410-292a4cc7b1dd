"use client";

import React, { useEffect, useState } from "react";
import BlogCard from "./BlogCard";
import { cn } from "@/lib/utils";
import { useBlog } from "@/contexts/BlogContext";
import LoadingSpinner from "@/components/common/LoadingSpinner";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const RecentActivity = () => {
  const {
    recentBlogs,
    recentBlogsLoading,
    recentBlogsError,
    fetchRecentBlogs
  } = useBlog();

  const [sortBy, setSortBy] = useState("publishedAt");

  useEffect(() => {
    fetchRecentBlogs({
      sortBy: sortBy as any,
      sortOrder: sortBy === "publishedAt" ? "desc" : "asc"
    });
  }, [sortBy]);

  const handleSortChange = (value: string) => {
    setSortBy(value);
  };

  // Split blogs into first row (2 blogs) and remaining blogs
  const firstRowBlogs = recentBlogs.slice(0, 2);
  const remainingBlogs = recentBlogs.slice(2);

  if (recentBlogsLoading) {
    return (
      <div className="w-full">
        <div className="mb-5 flex items-center justify-between">
          <h2 className="text-3xl font-bold text-accent lg:text-5xl">
            Recent Activity
          </h2>
        </div>
        <div className="flex justify-center items-center py-12">
          <LoadingSpinner width={40} height={40} />
        </div>
      </div>
    );
  }

  if (recentBlogsError) {
    return (
      <div className="w-full">
        <div className="mb-5 flex items-center justify-between">
          <h2 className="text-3xl font-bold text-accent lg:text-5xl">
            Recent Activity
          </h2>
        </div>
        <div className="text-center py-8">
          <p className="text-red-500 mb-4">{recentBlogsError}</p>
          <button
            onClick={() => fetchRecentBlogs()}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // Use only API data

  return (
    <div className="w-full">
      <div className="mb-5 flex items-center justify-between">
        <h2 className="text-3xl font-bold text-accent lg:text-5xl">
          Recent Activity
        </h2>
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600">Sort by:</span>
          <Select value={sortBy} onValueChange={handleSortChange}>
            <SelectTrigger className="w-[120px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="publishedAt">Newest</SelectItem>
              <SelectItem value="createdAt">Oldest</SelectItem>
              <SelectItem value="viewCount">Popular</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      {recentBlogs.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">No Data</p>
        </div>
      ) : (
        <>
          {/* First Row: Medium + Large Cards */}
          <div className="mb-8 grid grid-cols-1 gap-8 md:grid-cols-5">
            {firstRowBlogs.map((post, index) => (
              <div
                key={post._id}
                className={cn(index === 1 ? "md:col-span-3" : "md:col-span-2")}
              >
                <BlogCard
                  id={parseInt(post._id.slice(-6), 16) || 0}
                  title={post.title}
                  excerpt={post.content.substring(0, 150) + "..."}
                  image={"https://images.ctfassets.net/hrltx12pl8hq/28ECAQiPJZ78hxatLTa7Ts/2f695d869736ae3b0de3e56ceaca3958/free-nature-images.jpg?fit=fill&w=1200&h=630"}
                  tags={post.tags || []}
                  isLarge={index === 1}
                  isMedium={index === 0}
                  slug={post.slug || post._id}
                />
              </div>
            ))}
          </div>

          {/* Remaining Cards: 3-Column Grid */}
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            {remainingBlogs.map((post) => (
              <div key={post._id}>
                <BlogCard
                  id={parseInt(post._id.slice(-6), 16) || 0}
                  title={post.title}
                  excerpt={post.content.substring(0, 150) + "..."}
                  image={"https://images.ctfassets.net/hrltx12pl8hq/28ECAQiPJZ78hxatLTa7Ts/2f695d869736ae3b0de3e56ceaca3958/free-nature-images.jpg?fit=fill&w=1200&h=630"}
                  tags={post.tags || []}
                  isSmallMedium={true}
                  slug={post.slug || post._id}
                />
              </div>
            ))}
          </div>
        </>
      )}
    </div>
  );
};

export default RecentActivity;
